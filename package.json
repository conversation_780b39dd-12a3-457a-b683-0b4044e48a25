{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "server": "node server.js", "server:dev": "nodemon server.js", "server:prod": "NODE_ENV=production node server.js", "server:start": "node start-dev.js", "db:test": "node test-db-connection.js", "db:check": "node -e \"import('./src/config/db.js')\"", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "npm run server:start", "start:dev": "concurrently \"npm run dev\" \"npm run server:dev\"", "start:mobile": "node start-mobile-test.js", "start:pwa": "node start-pwa-test.js", "test:pwa": "node test-pwa-fixes.js", "restart:pwa": "node restart-pwa-test.js", "clean:push": "node clean-push-subscriptions.js", "diagnose:push": "node diagnose-push.js", "dev:frontend": "vite", "dev:backend": "nodemon server.js", "dev:full": "concurrently --names \"<PERSON><PERSON><PERSON><PERSON><PERSON>,BACKEND\" --prefix-colors \"cyan,yellow\" \"npm run dev:frontend\" \"npm run dev:backend\""}, "dependencies": {"@heroicons/react": "^2.2.0", "@neondatabase/serverless": "^1.0.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "bcrypt": "^6.0.0", "chart.js": "^4.4.9", "cors": "^2.8.5", "daisyui": "^5.0.35", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "formik": "^2.4.6", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.510.0", "moment": "^2.30.1", "multer": "^1.4.5-lts.2", "next": "^15.3.2", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "pg": "^8.16.0", "postcss": "^8.5.3", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "tailwindcss": "^4.1.7", "web-push": "^3.6.7", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4.1.7", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "nodemon": "^3.1.10", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-pwa": "^1.0.0"}, "description": "This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.", "main": "eslint.config.js", "keywords": [], "author": "", "license": "ISC"}