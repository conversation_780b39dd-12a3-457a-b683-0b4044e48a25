# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build files
dist/
build/
out/
.cache/

# Editor & IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Testing
coverage/

# Sensitive and script files
api-tests.http
generateHash.js
requests.http
*.http
**/auth.js
**/*.config.js
**/*.credentials.json
**/*.pem
**/*.key
**/*.cert

# Database related
sql/
**/*.sql
**/db.js

# Temp files
**/temp/
**/tmp/