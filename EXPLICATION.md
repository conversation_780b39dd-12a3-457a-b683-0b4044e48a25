# Plateforme de Gestion des Stages pour l'INSTI

## Problématique

L'institut fait actuellement face à plusieurs défis majeurs dans la gestion des stages :

### Problèmes Identifiés

1. **Gestion obsolète** : L'administration continue d'utiliser des fichiers Excel pour gérer tous les aspects des stages, ce qui est inefficace et ne reflète pas l'évolution technologique de 2025.

2. **Absence d'accompagnement** : L'institut ne propose pas de stages aux étudiants. Ces derniers doivent se débrouiller seuls pour trouver un lieu de stage, ce qui représente un défi considérable.Des etudiants se retouvent meme sans stage le jours du debut des stages!

3. **Manque de communication** : Aucun système centralisé n'existe pour communiquer des informations importantes aux étudiants si ce n'est que un groupe whatsapp ou tout le meonde envoie un message (dates de remise, procédures, etc.).

4. **Problème de duplication des thèmes** : Les étudiants reprennent souvent les mêmes thèmes de mémoire que les années antérieures sans apporter d'innovations, créant des duplicatas et rendant leurs projets de soutenance déjà existants. Lors de la surpervision nous a remarquer que nos superviseur emploi la phrase: "Ce theme est deja realisé ici vous apportez quoi de nouveau" En d'autres thme la majorité des themes sont du deja vue alors que les etudiants n'ont pas d'informations a ce propos

5. **Suivi inefficace** : Les équipes de supervision n'ont pas d'outils appropriés pour planifier leurs visites sur les lieux de stages qui est planifier princpalement a ciblant les dates de fin des stages avec notre outils ce filtres est disponibles.

## Notre Solution

### Plateforme Complète de Gestion des Stages

Notre plateforme révolutionne la gestion des stages en proposant une solution intégrée qui résout tous les problèmes identifiés :

#### Pour les Étudiants
- **Recherche de stages facilitée** : Accès aux propositions de stages publiées par l'administration selon leur filière
- **Candidature simplifiée** : Possibilité de postuler directement aux offres de stages disponibles en ayant les informations necessaire sur les personens a contacteer et les dossier a deposer pour obtenir le stage
- **Notifications personnalisées** : Réception d'alertes ciblées pour les informations importantes (dates de remise, procédures, etc.)
- **Base de données de thèmes** : Consultation des thèmes de soutenances des années antérieures avec leurs modifications possibles
- **Sélection de thèmes guidée** : Choix parmi les propositions de thèmes actuels pour éviter les duplicatas

#### Pour l'Administration
- **Tableau de bord analytique** : Vue d'ensemble avec graphiques montrant :
  - Étudiants avec stage par filière
  - Nombre d'étudiants par filière
  - Répartition des étudiants par entreprise
  - Comparaison stages trouvés vs étudiants par filière
- **Gestion centralisée** : Interface complète remplaçant les fichiers Excel avec filtres avancés (par exemple le filtres "date de fin de stage" pour planifier les supervisions remplace  la llongue liste d'attente du groupe whatsapp qui est relayé )
- **Proposition de stages** : Formulaire dédié pour publier des offres de stages
- **Gestion des thèmes** : Ajout de projets récents et propositions de thèmes de mémoire
- **Système de notification** : Envoi de messages ciblés ou groupés aux étudiants

Cette solution transforme complètement l'expérience de gestion des stages, passant d'un système archaïque basé sur Excel à une plateforme moderne, interactive et efficace qui bénéficie à tous les acteurs du processus.

## Technologies Utilisées

- **Frontend** : HTML, CSS, JavaScript avec Vite.js et React js 
- **Backend** : Node.js avec Express.js
- **Base de Données** :  PostgreSQL
